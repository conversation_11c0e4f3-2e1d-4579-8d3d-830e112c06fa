import React, { useState, useMemo } from 'react';
import { AppProvider, useApp } from './contexts/AppContext';
import Header from './components/layout/Header';
import HeroBanner from './components/HeroBanner';
import VoiceCarousel from './components/VoiceCarousel';
import SearchFilters from './components/SearchFilters';
import VoiceDetailModal from './components/VoiceDetailModal';
import VoiceCard from './components/VoiceCard';
import { VoiceModel, SearchFilters as SearchFiltersType, SortOption } from './types';

const AppContent: React.FC = () => {
  const { voiceData, loading, error, t } = useApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<SearchFiltersType>({});
  const [sortBy, setSortBy] = useState<SortOption>('rating');
  const [selectedVoice, setSelectedVoice] = useState<VoiceModel | null>(null);
  const [playingVoiceId, setPlayingVoiceId] = useState<string | null>(null);
  const [showSearch, setShowSearch] = useState(false);

  // Filter and sort voices
  const filteredVoices = useMemo(() => {
    if (!voiceData) return [];

    let voices = voiceData.models.filter((voice) => {
      // Text search
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = 
          voice.name.toLowerCase().includes(query) ||
          voice.description.toLowerCase().includes(query) ||
          voice.tags.some(tag => tag.toLowerCase().includes(query)) ||
          voice.characteristics.tone.toLowerCase().includes(query) ||
          voice.characteristics.accent.toLowerCase().includes(query);
        
        if (!matchesSearch) return false;
      }

      // Provider filter
      if (filters.provider && voice.provider !== filters.provider) {
        return false;
      }

      // Language filter
      if (filters.language && !voice.supported_languages.includes(filters.language)) {
        return false;
      }

      // Category filter
      if (filters.category && !voice.categories.includes(filters.category)) {
        return false;
      }

      // Premium filter
      if (filters.premium !== undefined && voice.premium !== filters.premium) {
        return false;
      }

      return true;
    });

    // Sort voices
    voices.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'rating':
          return b.rating - a.rating;
        case 'newest':
          // For demo purposes, we'll sort by name as a proxy for newest
          return b.name.localeCompare(a.name);
        default:
          return 0;
      }
    });

    return voices;
  }, [voiceData, searchQuery, filters, sortBy]);

  // Get voices by category
  const getVoicesByCategory = (category: string) => {
    if (!voiceData) return [];
    return voiceData.models.filter(voice => voice.categories.includes(category));
  };

  // Featured voice (first trending voice)
  const featuredVoice = useMemo(() => {
    return getVoicesByCategory('trending')[0] || voiceData?.models[0];
  }, [voiceData]);

  const handlePlayVoice = (voice: VoiceModel) => {
    if (playingVoiceId === voice.id) {
      setPlayingVoiceId(null);
    } else {
      setPlayingVoiceId(voice.id);
      // Simulate playing for 3 seconds
      setTimeout(() => setPlayingVoiceId(null), 3000);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setShowSearch(!!query || Object.keys(filters).length > 0);
  };

  const handleLearnMore = (voice: VoiceModel) => {
    setSelectedVoice(voice);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-lg">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (error || !voiceData) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <p className="text-lg text-red-500 mb-4">{error || t('common.error')}</p>
          <button 
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            {t('common.try_again')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <Header onSearch={handleSearch} />
      
      <main className="pt-20">
        {!showSearch && featuredVoice && (
          <HeroBanner
            featuredVoice={featuredVoice}
            onPlaySample={handlePlayVoice}
            onLearnMore={handleLearnMore}
          />
        )}

        {showSearch && (
          <SearchFilters
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            filters={filters}
            onFiltersChange={setFilters}
            sortBy={sortBy}
            onSortChange={setSortBy}
            resultsCount={filteredVoices.length}
          />
        )}

        <div className="container mx-auto px-4 py-8">
          {showSearch ? (
            /* Search Results Grid */
            <div>
              <h2 className="text-2xl font-bold text-white mb-6">Search Results</h2>
              {filteredVoices.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredVoices.map((voice) => (
                    <VoiceCard
                      key={voice.id}
                      voice={voice}
                      onPlay={handlePlayVoice}
                      onLearnMore={handleLearnMore}
                      isPlaying={playingVoiceId === voice.id}
                      size="medium"
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-400 text-lg">No voices found matching your criteria.</p>
                  <button
                    onClick={() => {
                      setSearchQuery('');
                      setFilters({});
                      setShowSearch(false);
                    }}
                    className="mt-4 text-red-400 hover:text-red-300"
                  >
                    Clear search and browse all voices
                  </button>
                </div>
              )}
            </div>
          ) : (
            /* Category Carousels */
            <div>
              {/* Trending Voices */}
              <VoiceCarousel
                title={t('categories.trending')}
                voices={getVoicesByCategory('trending')}
                onPlayVoice={handlePlayVoice}
                onLearnMore={handleLearnMore}
                playingVoiceId={playingVoiceId}
              />

              {/* New Additions */}
              <VoiceCarousel
                title={t('categories.new')}
                voices={getVoicesByCategory('new')}
                onPlayVoice={handlePlayVoice}
                onLearnMore={handleLearnMore}
                playingVoiceId={playingVoiceId}
              />

              {/* Best for Narration */}
              <VoiceCarousel
                title={t('categories.narration')}
                voices={getVoicesByCategory('narration')}
                onPlayVoice={handlePlayVoice}
                onLearnMore={handleLearnMore}
                playingVoiceId={playingVoiceId}
              />

              {/* Multilingual Models */}
              <VoiceCarousel
                title={t('categories.multilingual')}
                voices={getVoicesByCategory('multilingual')}
                onPlayVoice={handlePlayVoice}
                onLearnMore={handleLearnMore}
                playingVoiceId={playingVoiceId}
              />

              {/* French Voices */}
              <VoiceCarousel
                title={t('categories.french')}
                voices={getVoicesByCategory('french')}
                onPlayVoice={handlePlayVoice}
                onLearnMore={handleLearnMore}
                playingVoiceId={playingVoiceId}
              />

              {/* Celebrity & Character Voices */}
              <VoiceCarousel
                title={t('categories.celebrity')}
                voices={getVoicesByCategory('celebrity')}
                onPlayVoice={handlePlayVoice}
                onLearnMore={handleLearnMore}
                playingVoiceId={playingVoiceId}
              />
            </div>
          )}
        </div>
      </main>

      {/* Voice Detail Modal */}
      <VoiceDetailModal
        voice={selectedVoice}
        isOpen={!!selectedVoice}
        onClose={() => setSelectedVoice(null)}
        onPlaySample={handlePlayVoice}
        isPlayingSample={playingVoiceId === selectedVoice?.id}
      />

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800 py-8 mt-16">
        <div className="container mx-auto px-4 text-center text-gray-400">
          <div className="flex flex-wrap justify-center space-x-6 mb-4">
            <a href="#" className="hover:text-white transition-colors">{t('footer.about')}</a>
            <a href="#" className="hover:text-white transition-colors">{t('footer.contact')}</a>
            <a href="#" className="hover:text-white transition-colors">{t('footer.privacy')}</a>
            <a href="#" className="hover:text-white transition-colors">{t('footer.terms')}</a>
          </div>
          <p className="text-sm">
            &copy; 2025 {t('app.title')}. {t('app.tagline')}
          </p>
        </div>
      </footer>
    </div>
  );
};

function App() {
  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  );
}

export default App;
