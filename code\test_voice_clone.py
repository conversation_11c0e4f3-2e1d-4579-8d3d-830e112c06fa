import asyncio
from external_api.minimax import minimax_minimax__mcp__voice_clone

async def main():
    try:
        result = await minimax_minimax__mcp__voice_clone(
            file="dummy_audio.txt",
            text="Hello, this is a test.",
            voice_id="test_voice"
        )
        print(result)
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    asyncio.run(main())
